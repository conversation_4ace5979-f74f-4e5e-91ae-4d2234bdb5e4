export default {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {
    SERVER_URL: '"http://192.168.30.205:8301"', //dan
    // SERVER_URL: '"http://172.24.131.207:8301"', //燕玲
    // SERVER_URL: '"http://192.168.30.114:8301"', //zk
    // SERVER_URL: '"https://fx.sichuanair.com"', //测试&生产 通过APITYPE区分
    ASSETS_URL: '"https://fx.sichuanair.com/resources"',
    // COUPON_SERVER_URL: '"http://47.108.178.2/api"', //东软卡券接口url
    // SERVER_URL: 'https://appdit.sichuanair.com/hf',
    // ASSETS_URL: 'https://appdit.sichuanair.com/hf/resources',
    SENSORS_SERVER_URL: '"http://47.95.127.158:8106/sa?project=default"',
    // /*生产*/
    // APITYPE: '"api"',
    // ENVVERSION: '"release"',
    /*测试*/
    // APITYPE: '"api-uat"',
    // ENVVERSION: '"trial"',
    /*联调*/
    APITYPE: '"api"',
    ENVVERSION: '"trial"',
  },
  logger: {
    quiet: false,
    stats: true,
  },
  mini: {},
  h5: {},
};
