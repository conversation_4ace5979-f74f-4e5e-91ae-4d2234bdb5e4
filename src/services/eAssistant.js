import request from "@/utils/request";
// 客户经理认证
export async function customerManagerAuthentication(params, headers) {
  return request("/api/v1/customerManager/authentication", {
    method: "POST",
    body: params,
    headers,
  });
}
// 客户经理取消认证
export async function disAuthentication(params, headers) {
  return request("/api/v1/customerManager/disAuthentication", {
    method: "POST",
    body: params,
    headers,
  });
}
// 查询客户经理认证状态
export async function queryCustomerManager(params, headers) {
  return request("/api/v1/customerManager/queryCustomerManager", {
    method: "GET",
    body: params,
    headers,
  });
}
// 查询所有行业信息
export async function retrieveIndustryDictList(params, headers) {
  return request("/api/v1/contractCustomer/retrieveIndustryDictList", {
    method: "GET",
    body: params,
    headers,
  });
}

// 签约客户新增
export async function contractCustomerAdd(params, headers) {
  return request("/api/v1/contractCustomer/add", {
    method: "POST",
    body: params,
    headers,
  });
}
// 修改客户新增
export async function contractCustomerUpdate(params, headers) {
  return request("/api/v1/contractCustomer/update", {
    method: "POST",
    body: params,
    headers,
  });
}
// 查询省
export async function provinceList(params, headers) {
  return request("/api/v1/provinceCityArea/provinceList", {
    method: "GET",
    body: params,
    headers,
  });
}
// 查询市区
export async function getCityList(params, headers) {
  return request("/api/v1/provinceCityArea/cityList", {
    method: "GET",
    body: params,
    headers,
  });
}
// 查询县区
export async function getAreaList(params, headers) {
  return request("/api/v1/provinceCityArea/areaList", {
    method: "GET",
    body: params,
    headers,
  });
}
// // 查询结果
// export async function contractCustomerResult(params, headers) {
//   return request("/api/v1/contractCustomer/contractCustomerInfo", {
//     method: "GET",
//     body: params,
//     headers,
//   });
// }
// 根据工号和会员卡号查询预约客户信息
export async function queryByWorkNumberByCardNumber(params, headers) {
  return request("/api/v1/contractCustomer/queryContractCustomerByCardNumber", {
    method: "GET",
    body: params,
    headers,
  });
}
// 获取省市区
export async function getProvinceCityAreaList(params, headers) {
  return request("/api/v1/provinceCityArea/list", {
    method: "GET",
    body: params,
    headers,
  });
}
// 签约客户列表
export async function getContractCustomerList(params, headers) {
  return request("/api/v1/contractCustomer/page", {
    method: "GET",
    body: params,
    headers,
  });
}
// 签约客户详情
export async function getContractCustomerInfo(params, headers) {
  return request("/api/v1/contractCustomer/contractCustomerInfo", {
    method: "GET",
    body: params,
    headers,
  });
}
// 签约客户审核
export async function auditContractCustomer(params, headers) {
  return request("/api/v1/contractCustomer/audit", {
    method: "POST",
    body: params,
    headers,
  });
}
// 签约客户-审核数量
export async function auditCustomerNum(params, headers) {
  return request("/api/v1/contractCustomer/getAuditCount", {
    method: "GET",
    body: params,
    headers,
  });
}
// 获取客户经理邀请码
export async function getCustomerManagerInviteQrcode(params, headers) {
  // return request("/api/v1/wx/getCustomerManagerInviteQrcode", {
  return request("/api/v1/wx/getCustomerManagerInviteQrcode", {
    method: "POST",
    body: params,
    headers,
  });
}
// 脱敏数据
export async function updateDataStatus(params, headers) {
  return request("/api/v1/contractCustomer/updateDataStatus", {
    method: "GET",
    body: params,
    headers,
  });
}
// 获取跳转旅服小程序的权限token
export async function getFxAccessToken(params, headers) {
  return request("/api/v1/customerManager/getFxAccessToken", {
    method: "GET",
    body: params,
    headers,
  });
}
// 获取openId
export async function saveOpenId(params, headers) {
  return request("/api/v1/wx/saveOpenId", {
    method: "POST",
    body: params,
    headers,
  });
}
// 查询当前客户经理的收款码路径
export async function getPaymentCodePath(params, headers) {
  return request("/api/v1/customerManager/paymentCode/getPath", {
    method: "GET",
    body: params,
    headers,
  });
}
// 会员信息查询
export async function getMemberInfoDataQuery(params, headers) {
  return request("/api/v1/dataQuery/getMemberInfo", {
    method: "GET",
    body: params,
    headers,
  });
}

// 根据企业id查看审批记录
export async function getBenefitAuditRecordsById(params, headers) {
  return request(
    "/api/v1/companyBenefitSigningInfo/getBenefitAuditRecordsById",
    {
      method: "GET",
      body: params,
      headers,
    }
  );
}
// 根据企业id查看企业信息
export async function getCompanyBenefitById(params, headers) {
  return request("/api/v1/companyBenefitSigningInfo/getCompanyBenefitById", {
    method: "GET",
    body: params,
    headers,
  });
}
// 通过名称或者协议号查询获取权益库存列表
export async function getCompanyBenefitInfoById(params, headers) {
  return request(
    "/api/v1/companyBenefitSigningInfo/getCompanyBenefitInfoById",
    {
      method: "GET",
      body: params,
      headers,
    }
  );
}
// 根据企业名称或者协议号查询某客户经理下的所有签约企业
export async function getCompanyBenefitList(params, headers) {
  return request("/api/v1/companyBenefitSigningInfo/getCompanyBenefitList", {
    method: "GET",
    body: params,
    headers,
  });
}

// 提交审批单
export async function submitAuditApply(params, headers) {
  return request("/api/v1/companyBenefitSigningInfo/submitAuditApply", {
    method: "POST",
    body: params,
    headers,
  });
}

// 查询审批单结果
export async function getAuditApplyByApplyId(params, headers) {
  return request("/api/v1/companyBenefitSigningInfo/getAuditApplyByApplyId", {
    method: "GET",
    body: params,
    headers,
  });
}
// 文件上传
export async function commonUpload(params, headers) {
  return request("/api/v1/common/upload", {
    method: "GET",
    body: params,
    headers,
  });
}

// //获取中台token
// export async function getCouponToken(params, headers) {
//   return request("/api-mng/authorize", {
//     method: "POST",
//     body: params,
//     headers,
//   });
// }
//我的优惠券 http://47.108.178.2/api/bff-service/bff_service/coupon/getCouponsByUserId
export async function getCouponsByUserId(params, headers) {
  return request("/api/v1/benfitGrant/getBenfitCouponsByUserId", {
    method: "GET",
    body: params,
    headers,
  });
}
//获取卡券二维码
export async function getCouponsErCode(params, headers) {
  return request("/api/v1/benfitGrant/getBenefitQrBase64", {
    method: "POST",
    body: params,
    headers,
  });
}
//转增卡券
export async function sendCoupon(params, headers) {
  return request("/api/v1/benfitGrant/getBenefitGifts", {
    method: "POST",
    body: params,
    headers,
  });
}//转增卡券
export async function getDecryptWorkNumberKey(params, headers) {
  return request("/api/v1/common/getDecryptWorkNumberKey", {
    method: "POST",
    body: params,
    headers,
  });
}
