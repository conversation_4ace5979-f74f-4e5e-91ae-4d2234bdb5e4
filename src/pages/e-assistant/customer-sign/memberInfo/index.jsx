import { memo, useEffect, useState } from "react";
import NavigationBar from "../../../common/navigation-bar";
import { Image, Input, Switch, Text, View } from "@tarojs/components";
import "./index.less";
import ShowEncModal from "../components/showEncModal";
import { getMemberInfoDataQuery } from "../../../../services/eAssistant";
import { REQUEST_SUCCESS } from "../../../common";
import DescItem from "../components/DescItem";
import {
  memberInfoEmpty,
  realNameFalse,
  realNameTrue,
  searchIcon,
} from "../../img";
import { docTypeOption, sexList } from "../../../../utils/commonDefine";
import Watermark from "../../components/watermark";

const MemberInfo = () => {
  const [detailData, setDetailData] = useState();
  const [showEncryption, setShowEncryption] = useState(true); //展示加密信息switch
  const [isEncModalShow, setIsEncModalShow] = useState(false); //加密信息弹窗
  const [searchInp, setSearchInp] = useState(""); //搜索
  const [isChecked, setIsChecked] = useState(false);
  //获取详情
  const getDetail = async (isDecrypt, inputVal,key) => {
    const { code, data } = await getMemberInfoDataQuery({
      queryCondition: inputVal || searchInp,
      isDecrypt: !!isDecrypt,decryptKey:key
    });
    if (code === REQUEST_SUCCESS) {
      setDetailData(data);
      console.log(
        docTypeOption.find((item) => item.value === data?.certificateType)
          ?.label
      );
    }
  };

  useEffect(() => {}, []);
  return (
    <>
      <View className={"member-info-page"}>
        <Watermark />
        <NavigationBar
          bgColor={"#fff"}
          title={"会员信息查询"}
          titleColor={"#000"}
        />
        <View className={"search-box"}>
          <Input
            className={"search-inp"}
            placeholder={"会员号/手机号/身份证号"}
            value={searchInp}
            onInput={(ev) => setSearchInp(ev?.target?.value)}
            onConfirm={(ev) => {
              setSearchInp(ev?.target?.value);
              getDetail(isChecked, ev?.target?.value);
            }}
          />
          <View className={"search-icon-box"} onClick={() => getDetail()}>
            <Image src={searchIcon} className={"search-icon"} />
          </View>
        </View>

        {detailData ? (
          <>
            <View className={"show-info card-box"}>
              <View>展示加密信息</View>
              <Switch
                checked={isChecked}
                color={"#1663fa"}
                disabled={showEncryption}
                onClick={() => {
                  if (isChecked) {
                    setShowEncryption(true);
                    setIsChecked(false);
                    getDetail(false);
                  } else {
                    setIsEncModalShow(true);
                  }
                }}
              />
            </View>
            <View className={"card-box "}>
              <View className={"card-header"}>
                <View className={"name-box"}>
                  <View className={"left"}>
                    <Text className={"name"}>{detailData?.memberNameZh}</Text>
                    <Text className={"english-name"}>
                      {detailData?.memberNameEn}
                    </Text>
                    <Text className={"sex"}>
                      {
                        sexList.find((item) => item.value === detailData?.sex)
                          ?.label
                      }
                    </Text>
                  </View>
                  <View className={"real-name-status"}>
                    {detailData?.realNameFlag === "Y" && (
                      <Image src={realNameTrue} />
                    )}
                    {detailData?.realNameFlag === "N" && (
                      <Image src={realNameFalse} />
                    )}
                  </View>
                </View>
              </View>
              <View className={"desc-list"}>
                <DescItem
                  isHorizontal={true}
                  title={"手机号"}
                  value={detailData?.mobilePhone}
                />
                <DescItem
                  isHorizontal={true}
                  title={"证件类型"}
                  value={
                    docTypeOption.find(
                      (item) => item.value === detailData?.certificateType
                    )?.label
                  }
                />
                <DescItem
                  isHorizontal={true}
                  title={"证件号码"}
                  value={detailData?.certificateId}
                />
                <DescItem
                  isHorizontal={true}
                  title={"生日"}
                  value={detailData?.birthday}
                />
              </View>
            </View>
            <View className={"card-box "}>
              <View className={"desc-list"}>
                <DescItem
                  isHorizontal={true}
                  title={"会员卡号"}
                  value={detailData?.memberCardNumber}
                />
                <DescItem
                  isHorizontal={true}
                  title={"当前会员级别"}
                  value={detailData?.tierCode}
                />
                <DescItem
                  isHorizontal={true}
                  title={"当前级别失效期"}
                  value={detailData?.tierExpirationDate}
                />
                <View className={"split-line"}></View>

                <View className={"card-color-content"}>
                  <View className={"color-title"}>保级所需达到</View>
                  <View className={"color-content"}>
                    <View className={"color-item"}>
                      定级航段
                      <Text>
                        {detailData?.tierContinueQualifiedSegment || "--"}
                      </Text>
                    </View>
                    <View className={"color-item"}>
                      定级里程
                      <Text>
                        {detailData?.tierContinueQualifiedPoint || "--"}
                      </Text>
                    </View>
                  </View>
                </View>
                <View className={"card-color-content"}>
                  <View className={"color-title"}>升级目标级别所需达到</View>
                  <View className={"color-content"}>
                    <View className={"color-item"}>
                      定级航段
                      <Text>
                        {detailData?.upgradeTierQualifiedSegment || "--"}
                      </Text>
                    </View>
                    <View className={"color-item"}>
                      定级里程
                      <Text>
                        {detailData?.upgradeTierQualifiedPoint || "--"}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
            <View className={"card-box "}>
              <View className={"desc-list"}>
                <DescItem
                  isHorizontal={true}
                  title={"可消费里程"}
                  value={detailData?.validPoint}
                />
                <DescItem
                  isHorizontal={true}
                  title={"近三个月即将失效里程"}
                  value={detailData?.expirePointThirdMonth}
                />
                <DescItem
                  isHorizontal={true}
                  title={"近12个月定级里程"}
                  value={detailData?.totalQualifiedPointRecentPeriod}
                />
                <DescItem
                  isHorizontal={true}
                  title={"近12个月定级航段"}
                  value={detailData?.totalQualifiedSegmentRecentPeriod}
                />
              </View>
            </View>
          </>
        ) : (
          <View className={"empty-box"}>
            <Image src={memberInfoEmpty} />
          </View>
        )}
      </View>
      {/*展示加密信息*/}
      <ShowEncModal
        show={isEncModalShow}
        editData={detailData}
        onClose={(data,key) => {
          if (data) {
            getDetail(true,searchInp,key).then(() => {
              setIsEncModalShow(false);
              setShowEncryption(true);
              setIsChecked(!isChecked);
            });
          } else {
            setIsEncModalShow(false);
            setShowEncryption(false);
            setIsChecked(!isChecked);
          }
        }}
      />
    </>
  );
};
export default memo(MemberInfo);
