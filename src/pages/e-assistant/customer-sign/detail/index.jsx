import { memo, useEffect, useState } from "react";
import NavigationBar from "../../../common/navigation-bar";
import { Button, Image, Switch, Text, View } from "@tarojs/components";
import "./index.less";
import DescItem from "../components/DescItem";
import { signError, signProcess, signSuccess } from "../../img";
import StatusTag from "../components/StatusTag";
import Taro, { useRouter } from "@tarojs/taro";
import AuditModal from "../components/AuditModal";
import ShowEncModal from "../components/showEncModal";
import { getContractCustomerInfo } from "../../../../services/eAssistant";
import { REQUEST_SUCCESS } from "../../../common";
import { encData } from "../const";
import Contract from "../components/Contract";
import Watermark from "../../components/watermark";

const CustomerInvite = () => {
  const pageId = useRouter()?.params?.id;

  const [detailData, setDetailData] = useState();
  const [auditModalShow, setAuditModalShow] = useState(false);
  const [showEncryption, setShowEncryption] = useState(true); //展示加密信息switch
  const [isEncModalShow, setIsEncModalShow] = useState(false); //加密信息弹窗

  //获取列表
  const getDetail = async (isDecrypt = false, key) => {
    const { code, data } = await getContractCustomerInfo({
      contractCustomerId: pageId,
      isDecrypt: !!isDecrypt,
      decryptKey: key,
    });

    if (code === REQUEST_SUCCESS) {
      setDetailData({ ...encData(data), isEnc: !!isDecrypt });
    }
  };
  useEffect(() => {
    getDetail();
  }, []);

  return (
    <>
      <View className={"custom-detail"}>
        <Watermark />
        <NavigationBar
          bgColor={"transparent"}
          title={"签约详情"}
          titleColor={"#000"}
        />
        <View className={"custom-detail-content"}>
          <View className={"show-info card-box"}>
            <View>展示加密信息</View>
            <Switch
              checked={!!detailData?.isEnc}
              color={"#1663fa"}
              disabled={showEncryption}
              onClick={() => {
                if (detailData?.isEnc) {
                  getDetail(false);
                } else {
                  setIsEncModalShow(true);
                }
              }}
            />
          </View>
          <View className={"card-box detail-content"}>
            {(!detailData?.auditStatus || detailData?.auditStatus === "1") && (
              <Image src={signProcess} className={"sign-status"} />
            )}
            {detailData?.auditStatus === "2" && (
              <Image src={signSuccess} className={"sign-status"} />
            )}
            {detailData?.auditStatus === "3" && (
              <Image src={signError} className={"sign-status"} />
            )}
            <View className={"card-header"}>签约公司/单位信息</View>
            <View className={"desc-list"}>
              <DescItem title={"大客户编码"} value={detailData?.customerCode} />
              <DescItem title={"企业名称"} value={detailData?.enterpriseName} />
              <DescItem
                title={"企业地址"}
                value={`${detailData?.companyAllAddress}`}
              />
              <DescItem title={"企业性质"} value={detailData?.industryType} />
              <DescItem
                title={"签约人姓名"}
                value={detailData?.contractCustomName}
              />
              <DescItem
                title={"签约人手机号"}
                value={detailData?.contractCustomPhone}
              />
              <DescItem title={"联系人姓名"} value={detailData?.contactName} />
              <DescItem
                title={"联系人手机号"}
                value={detailData?.contactPhone}
              />
              <DescItem
                title={"营业执照"}
                value={
                  <Image
                    src={detailData?.businessLicensePath}
                    onClick={() => {
                      Taro.previewImage({
                        urls: [detailData?.businessLicensePath],
                      });
                    }}
                    style={{ width: 80, height: 80 }}
                  />
                }
              />
              <DescItem
                title={"B2G名单提交状态"}
                value={
                  <>
                    {detailData?.whitelistStatus === "1" && (
                      <StatusTag status={"success"} title={"已上传"} />
                    )}{" "}
                    {(!detailData?.whitelistStatus ||
                      detailData?.whitelistStatus === "0") && (
                      <StatusTag status={"normal"} title={"未上传"} />
                    )}
                  </>
                }
              />
            </View>
          </View>
          <View className={"card-box detail-content"}>
            <View className={"card-header"}>合同协议</View>
            <Contract />
          </View>
          <View className={"card-box detail-content"}>
            <View className={"card-header"}>客户确认</View>
            <View className={"desc-list"}>
              <DescItem
                title={"签约人电子签名"}
                value={
                  <View className={"sign-name"}>
                    <Image
                      src={detailData?.signaturePath}
                      onClick={() => {
                        Taro.previewImage({
                          urls: [detailData?.signaturePath],
                        });
                      }}
                    />
                  </View>
                }
              />
            </View>
          </View>
          {(!detailData?.auditStatus || detailData?.auditStatus === "1") && (
            <View className={"btn-box"}>
              <Button
                type={"primary"}
                onClick={() => {
                  setAuditModalShow(true);
                }}
              >
                审核
              </Button>
            </View>
          )}
        </View>
      </View>
      {/*  审核弹窗*/}
      <AuditModal
        show={auditModalShow}
        editData={detailData}
        onClose={(data) => {
          setAuditModalShow(false);
          if (data) {
            getDetail();
          }
        }}
      />
      {/*展示加密信息*/}
      <ShowEncModal
        show={isEncModalShow}
        editData={detailData}
        onClose={(data, key) => {
        //   console.log(22222);
        //   setIsEncModalShow(false);
        //   if (data) {
        //     setShowEncryption(true);
        //     getDetail(true, key);
        //   } else {
        //     setShowEncryption(false);
        //     setDetailData({ ...detailData, isEnc: false });
        //   }
        // }}
      />
    </>
  );
};
export default memo(CustomerInvite);
