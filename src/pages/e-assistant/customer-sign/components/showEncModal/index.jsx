import { memo, useEffect, useState } from "react";
import "./index.less";
import { AtButton, AtCurtain } from "taro-ui";
import { Form, Input, View } from "@tarojs/components";
import DescItem from "../DescItem";
import { localStorage } from "@/utils/utils";
import { toast } from "../../../../../utils/utils";
import { REQUEST_SUCCESS } from "../../../../common";
import { getDecryptWorkNumberKey } from "../../../../../services/eAssistant";

const ShowEncModal = ({ show, onClose }) => {
  const customerMemberInfo = localStorage?.getItem("customerMemberInfo")
    ? JSON.parse(localStorage?.getItem("customerMemberInfo"))
    : {};

  const [workNumber, setWorkNumber] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!workNumber || workNumber !== customerMemberInfo?.workNumber) {
      // message.warning("工号验证不通过！");
      toast.info("工号验证不通过！");
      return;
    }
    setLoading(true);
    const { message: msg, code, data } = await getDecryptWorkNumberKey();
    if (code === REQUEST_SUCCESS) {
      setWorkNumber("");
      onClose?.(true, data);
      setLoading(false);
      toast.info("操作成功！");
    } else {
      toast.info(msg || "操作失败！");
      setLoading(false);
    }
  };

  return (
    <>
      {show ? (
        <AtCurtain
          isOpened={show}
          onClose={() => {
            onClose?.();
          }}
          closeBtnPosition={"top-right"}
        >
          <View className={"card-box audit-modal"}>
            <View className={"card-header"}>展示加密信息</View>
            <Form onSubmit={handleSubmit}>
              <View className={"desc-list"}>
                <DescItem
                  title={""}
                  value={
                    <Input
                      placeholder="请输入工号进行验证"
                      value={workNumber}
                      onInput={(ev) => {
                        setWorkNumber(ev?.target?.value);
                      }}
                    />
                  }
                />
              </View>
              <View className={"btn-box"}>
                <AtButton loading={loading} type="primary" formType={"submit"}>
                  验证
                </AtButton>
              </View>
            </Form>
          </View>
        </AtCurtain>
      ) : null}
    </>
  );
};
export default memo(ShowEncModal);
