import { memo, useState, useEffect, useRef } from "react";
import "./index.less";
import { AtIcon } from "taro-ui";
import Taro, { useRouter } from "@tarojs/taro";
import {
  Button,
  Image,
  View,
  Form,
  Input,
  Picker,
  Text,
  Canvas,
  Checkbox,
} from "@tarojs/components";
import * as imgs from "../../../img";
import CanvasSign from "../CanvasSign";
import { rightIcon, signImg } from "../../../img";
import { localStorage, toast } from "@/utils/utils";
import RadioSelf from "../../../components/RadioSelf";
import OutPdf from "../OutPdf";
import {
  retrieveIndustryDictList,
  contractCustomerAdd,
  getProvinceCityAreaList,
  contractCustomerUpdate,
} from "@/services/eAssistant";
import NavigationBar from "../../../../common/navigation-bar";
import Contract from "../../../customer-sign/components/Contract";
import {getContractCustomerInfo, getDecryptWorkNumberKey} from "../../../../../services/eAssistant";
import {REQUEST_SUCCESS} from "../../../../common";

const formRules = {
  enterpriseName: "请填写企业名称！",
  enterpriseProvinceNum: "请填写企业地址省区！",
  enterpriseCityNum: "请填写企业地址市区！",
  enterpriseAreaNum: "请填写企业地址县区！",
  enterpriseAddress: "请填写企业地址详细地址！",
  mailProvinceNum: "请填写接收省区！",
  mailCityNum: "请填写接收市区！",
  mailAreaNum: "请填写接收县区！",
  mailAddress: "请填写接收详细地址",
  industryType: "请选择企业性质",
  intineraryType: "请选择行程单接收方式！",
  contractCustomName: "请填写签约人姓名！",
  contractCustomPhone: "请填写签约人电话！",
  contactName: "请填写联系人姓名！",
  contactPhone: "请填写联系人电话！",
  businessLicenseFid: "请上传公司营业执照！",
  signatureFid: "请签字！",
  mailName: "请填写收件人姓名",
  mailPhone: "请填写收件人手机号",
  // protocolFid: "请重新签名",
};
const SignForm = ({ getResult, type, id, isBind }) => {
  const createId =
    useRouter()?.params?.customerId ||
    decodeURIComponent(useRouter()?.params?.scene)?.split("=")?.[1];

  console.log(createId, "====createId====");
  const [signImgs, setSignImgs] = useState("");
  const [addressList, setAddressList] = useState({
    provinces: [],
    cities: [],
    areas: [],
  });
  const [enterpriseAddressList, setEnterpriseAddressList] = useState({
    provinceObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
    cityObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
    areaObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
  }); //企业地址
  const [mailAddressList, setMailAddressList] = useState({
    provinceObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
    cityObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
    areaObj: {
      options: [],
      codeObj: [],
      labelObj: [],
    },
  }); //寄送地址
  const picCanvasRef = useRef();
  const [ownerSignUrl, setOwnerSignUrl] = useState(undefined);
  const [uploadUrl, setUploadUrl] = useState(undefined); //上传图片
  const [signLabel, setSignLabel] = useState("1");
  const [formData, setFormData] = useState({
    enterpriseName: "", //企业名称
    enterpriseProvinceNum: "",
    enterpriseCityNum: "",
    enterpriseAddress: "",
    enterpriseAreaNum: "",
    mailProvinceNum: "",
    mailCityNum: "",
    mailAreaNum: "",
    mailAddress: "",
    industryType: "",
    intineraryType: "1",
    contractCustomName: "",
    contractCustomPhone: "",
    contactName: "",
    contactPhone: "",
    businessLicenseFid: undefined,
    signatureFid: undefined,
    protocolFid: undefined,
    mailName: "",
    mailPhone: "",
  });
  const [industryList, setIndustryList] = useState({
    options: [],
    valuesObj: {},
  }); //行业数据
  const [isReadContract, setIsReadContract] = useState(false);

  // 检验数据是否填充完整，并保存不完整数据的键值对
  const getEmptyFields = (formData, prefix = "") => {
    const emptyFields = [];

    Object?.entries(formData)?.forEach(([key, value]) => {
      if (typeof value === "object" && value !== null) {
        const nestedEmptyFields = getEmptyFields(value, key);
        emptyFields.push(...nestedEmptyFields);
      } else if (value === "" || value === undefined) {
        emptyFields.push(prefix ? `${prefix}.${key}` : key);
      }
    });

    return emptyFields;
  };

  // 获取详情
  const getSignInfo = async (contractCustomerId) => {
    const { message: msg, code,data } = await getDecryptWorkNumberKey();
    if (code === REQUEST_SUCCESS) {
      setWorkNumber("");
      onClose?.(true,data);
      setLoading(false);
      toast.info("操作成功！");
    } else {
      toast.info(msg || "操作失败！");
      setLoading(false);
    }
    const res = await getContractCustomerInfo({
      contractCustomerId,
      isDecrypt: true,
    });
    if (res.code !== 200) return;
    setIsReadContract(true);
    const {
      enterpriseName,
      contractCustomName,
      contractCustomPhone,
      contactName,
      contactPhone,
      intineraryType,
      mailAddress,
      enterpriseAddress,
      signaturePath,
      businessLicensePath,
      businessLicenseFid,
      signatureFid,
      industryId,
      mailName,
      mailPhone,
      protocolFid,
    } = res.data || {};
    setUploadUrl(businessLicensePath);
    setOwnerSignUrl(signaturePath);

    const {
      enterprisePro,
      enterpriseCity,
      enterpriseArea,
      mailPro,
      mailCity,
      mailArea,
    } = await refreshAddressList(res.data);

    const { labelObj } = await getIndustryList();
    setFormData({
      enterpriseName,
      enterpriseProvinceNum: enterprisePro,
      enterpriseCityNum: enterpriseCity,
      enterpriseAddress,
      enterpriseAreaNum: enterpriseArea,
      mailPhone: intineraryType == 1 ? mailPhone : undefined,
      mailName: intineraryType == 1 ? mailName : undefined,
      mailProvinceNum: intineraryType == 1 ? mailPro : undefined,
      mailCityNum: intineraryType == 1 ? mailCity : undefined,
      mailAreaNum: intineraryType == 1 ? mailArea : undefined,
      mailAddress: intineraryType == 1 ? mailAddress : undefined,
      industryType: industryId ? labelObj[industryId] : undefined,
      intineraryType,
      contractCustomName,
      contractCustomPhone,
      contactName,
      contactPhone,
      businessLicenseFid,
      signatureFid,
      protocolFid,
    });
    if (signaturePath) {
      // setSignLabel("3");
      setSignLabel("2");
      //  setTimeout(()=>{
      //   changeLocalPic(signaturePath)
      //  },200)
    }
  };
  useEffect(() => {
    // getIndustryList(); //获取企业性质数据
    // getIndustryList(); //获取企业性质数据
    //   getAddressList(); //获取省市区地址

    if (type === "refresh" && id) {
      getSignInfo(id);
    } else {
      getIndustryList(); //获取企业性质数据
      getAddressList(); //获取省市区地址
    }
  }, [isBind]);

  // 获取行业数据
  const getIndustryList = async () => {
    const res = await retrieveIndustryDictList({});
    const obj = {
      options: res?.data?.map((item) => item.industryType),
      valuesObj: res?.data?.reduce((accumulator, obj) => {
        accumulator[obj.industryType] = obj.id;
        return accumulator;
      }, {}),
      labelObj: res?.data?.reduce((accumulator, obj) => {
        accumulator[obj.id] = obj.industryType;
        return accumulator;
      }, {}),
    };

    setIndustryList(obj);
    return obj;
  };

  // 获取对应市区、县区数据-企业地址
  const getEnterpriseAddressList = (type, value) => {
    if (type === "city") {
      const { cities } = addressList;
      const { options, codeObj } = enterpriseAddressList.provinceObj;
      const code = codeObj[options[value]];
      const cityOption = cities?.filter((item) => item.provinceNum === code);
      setEnterpriseAddressList({
        ...enterpriseAddressList,
        cityObj: {
          options: cityOption?.map((item) => item.cityName),
          labelObj: cityOption?.reduce((accumulator, obj) => {
            accumulator[obj.cityNum] = obj.cityName;
            return accumulator;
          }, {}),
          codeObj: cityOption?.reduce((accumulator, obj) => {
            accumulator[obj.cityName] = obj.cityNum;
            return accumulator;
          }, {}),
        },
      });
    }
    if (type === "area") {
      const { areas } = addressList;

      const { options, codeObj } = enterpriseAddressList.cityObj;

      const code = codeObj[options[value]];
      const areaOption = areas?.filter((item) => item.cityNum === code);

      setEnterpriseAddressList({
        ...enterpriseAddressList,
        areaObj: {
          options: areaOption?.map((item) => item.areaName),
          labelObj: areaOption?.reduce((accumulator, obj) => {
            accumulator[obj.areaNum] = obj.areaNum;
            return accumulator;
          }, {}),
          codeObj: areaOption?.reduce((accumulator, obj) => {
            accumulator[obj.areaName] = obj.areaNum;
            return accumulator;
          }, {}),
        },
      });
    }
  };
  // 获取对应市区、县区数据-寄送地址
  const getMailAddressList = (type, value) => {
    if (type === "city") {
      const { cities } = addressList;
      const { options, codeObj } = mailAddressList.provinceObj;
      const code = codeObj[options[value]];
      const cityOption = cities?.filter((item) => item.provinceNum === code);
      setMailAddressList({
        ...mailAddressList,
        cityObj: {
          options: cityOption?.map((item) => item.cityName),
          labelObj: cityOption?.reduce((accumulator, obj) => {
            accumulator[obj.cityNum] = obj.cityName;
            return accumulator;
          }, {}),
          codeObj: cityOption?.reduce((accumulator, obj) => {
            accumulator[obj.cityName] = obj.cityNum;
            return accumulator;
          }, {}),
        },
      });
    }
    if (type === "area") {
      const { areas } = addressList;

      const { options, codeObj } = mailAddressList.cityObj;

      const code = codeObj[options[value]];
      const areaOption = areas?.filter((item) => item.cityNum === code);

      setMailAddressList({
        ...mailAddressList,
        areaObj: {
          options: areaOption?.map((item) => item.areaName),
          labelObj: areaOption?.reduce((accumulator, obj) => {
            accumulator[obj.areaNum] = obj.areaNum;
            return accumulator;
          }, {}),
          codeObj: areaOption?.reduce((accumulator, obj) => {
            accumulator[obj.areaName] = obj.areaNum;
            return accumulator;
          }, {}),
        },
      });
    }
  };
  // 获取省市区数据
  const getAddressList = async () => {
    const res = await getProvinceCityAreaList();
    if (res.code !== 200) return;

    let provinces = [];
    let cities = [];
    let areas = [];

    res?.data?.forEach((province) => {
      provinces.push({
        provinceNum: province.provinceNum,
        provinceName: province.provinceName,
      });

      province.publicCityList.forEach((city) => {
        cities.push({
          cityNum: city.cityNum,
          cityName: city.cityName,
          provinceNum: city.provinceNum,
        });

        city.publicAreaList.forEach((area) => {
          areas.push({
            areaNum: area.areaNum,
            areaName: area.areaName,
            cityNum: area.cityNum,
          });
        });
      });
    });
    setAddressList({
      provinces,
      cities,
      areas,
    });
    const options = provinces?.map((item) => item.provinceName);
    const labelObj = provinces?.reduce((accumulator, obj) => {
      accumulator[obj.provinceNum] = obj.provinceName;
      return accumulator;
    }, {});
    const codeObj = provinces?.reduce((accumulator, obj) => {
      accumulator[obj.provinceName] = obj.provinceNum;
      return accumulator;
    }, {});

    setEnterpriseAddressList({
      ...enterpriseAddressList,
      provinceObj: {
        options,
        labelObj,
        codeObj,
      },
    });
    setMailAddressList({
      ...mailAddressList,
      provinceObj: {
        options,
        labelObj,
        codeObj,
      },
    });

    return {
      addressList: {
        provinces,
        cities,
        areas,
      },
      provinceObj: {
        options,
        labelObj,
        codeObj,
      },
    };
  };
  // 查询详情时获取数据源
  const refreshAddressList = async (data) => {
    const {
      enterpriseProvinceNum,
      enterpriseCityNum,
      enterpriseAreaNum,
      mailProvinceNum,
      intineraryType,
      mailCityNum,
      mailAreaNum,
    } = data;

    const { provinceObj, addressList } = await getAddressList();

    const { areas, cities, provinces } = addressList;
    let mailPro = undefined;
    let mailCity = undefined;
    let mailArea = undefined;
    const enterprisePro = provinces?.filter(
      (item) => item.provinceNum === enterpriseProvinceNum
    )?.[0]?.provinceName;
    const enterpriseCity = cities?.filter(
      (item) => item.cityNum === enterpriseCityNum
    )?.[0]?.cityName;
    const enterpriseArea = areas?.filter(
      (item) => item.areaNum === enterpriseAreaNum
    )?.[0]?.areaName;
    const cityList = cities?.filter(
      (item) => item.provinceNum === enterpriseProvinceNum
    );
    const areaList = areas?.filter(
      (item) => item.cityNum === enterpriseCityNum
    );
    setEnterpriseAddressList({
      ...enterpriseAddressList,
      provinceObj,
      cityObj: {
        options: cityList?.map((item) => item.cityName),
        labelObj: cityList?.reduce((accumulator, obj) => {
          accumulator[obj.cityNum] = obj.cityName;
          return accumulator;
        }, {}),
        codeObj: cityList?.reduce((accumulator, obj) => {
          accumulator[obj.cityName] = obj.cityNum;
          return accumulator;
        }, {}),
      },
      areaObj: {
        options: areaList?.map((item) => item.areaName),
        labelObj: areaList?.reduce((accumulator, obj) => {
          accumulator[obj.areaNum] = obj.areaNum;
          return accumulator;
        }, {}),
        codeObj: areaList?.reduce((accumulator, obj) => {
          accumulator[obj.areaName] = obj.areaNum;
          return accumulator;
        }, {}),
      },
    });

    // const
    if (intineraryType == 1) {
      mailPro = provinces?.filter(
        (item) => item.provinceNum === mailProvinceNum
      )?.[0]?.provinceName;
      mailCity = cities?.filter((item) => item.cityNum === mailCityNum)?.[0]
        ?.cityName;
      mailArea = areas?.filter((item) => item.areaNum === mailAreaNum)?.[0]
        ?.areaName;
      const mailCityList = cities?.filter(
        (item) => item.provinceNum === mailProvinceNum
      );
      const mailAreaList = areas?.filter(
        (item) => item.cityNum === mailCityNum
      );
      setMailAddressList({
        ...enterpriseAddressList,
        provinceObj,
        cityObj: {
          options: mailCityList?.map((item) => item.cityName),
          labelObj: mailCityList?.reduce((accumulator, obj) => {
            accumulator[obj.cityNum] = obj.cityName;
            return accumulator;
          }, {}),
          codeObj: mailCityList?.reduce((accumulator, obj) => {
            accumulator[obj.cityName] = obj.cityNum;
            return accumulator;
          }, {}),
        },
        areaObj: {
          options: mailAreaList?.map((item) => item.areaName),
          labelObj: mailAreaList?.reduce((accumulator, obj) => {
            accumulator[obj.areaNum] = obj.areaNum;
            return accumulator;
          }, {}),
          codeObj: mailAreaList?.reduce((accumulator, obj) => {
            accumulator[obj.areaName] = obj.areaNum;
            return accumulator;
          }, {}),
        },
      });
    }

    return {
      enterprisePro,
      enterpriseCity,
      enterpriseArea,
      mailPro,
      mailCity,
      mailArea,
    };
  };

  const handleSubmit = async (e) => {
    // getUserMsg();
    e.preventDefault();
    // 处理表单提交逻辑
    const emptyFields = getEmptyFields(formData);

    console.log(emptyFields);

    if (emptyFields.length > 0) {
      if (formData.intineraryType == 2) {
        // 自行打印除开接收地址
        const newEmptyFields = emptyFields?.filter(
          (item) =>
            item !== "mailProvinceNum" &&
            item !== "mailCityNum" &&
            item !== "mailAreaNum" &&
            item !== "mailAddress" &&
            item !== "mailName" &&
            item !== "mailPhone"
        );

        if (newEmptyFields?.length !== 0) {
          toast.info(formRules[newEmptyFields[0]] || "请输入必填项");
          return;
        }
      } else {
        if (emptyFields?.length !== 0) {
          toast.info(formRules[emptyFields[0]] || "请输入必填项");
          return;
        }
      }
    }
    const {
      businessLicenseFid,
      contactName,
      contactPhone,
      contractCustomName,
      contractCustomPhone,
      enterpriseAddress,
      enterpriseAreaNum,
      enterpriseCityNum,
      enterpriseName,
      enterpriseProvinceNum,
      industryType,
      intineraryType,
      mailAddress,
      mailAreaNum,
      mailCityNum,
      mailProvinceNum,
      signatureFid,
      mailName,
      mailPhone,
      protocolFid,
    } = formData || {};
    const phoneRegex = /^1[0-9]{10}$/; // 手机号格式验证正则表达式
    if (intineraryType === "1" && !phoneRegex.test(mailPhone)) {
      toast.info("请输入有效的收件人电话");
      return;
    }
    if (!phoneRegex.test(contactPhone)) {
      toast.info("请输入有效的联系人电话");
      return;
    }
    if (!phoneRegex.test(contractCustomPhone)) {
      toast.info("请输入有效的签约人电话");
      return;
    }
    const params = {
      businessLicenseFid,
      contactName,
      contactPhone,
      contractCustomName,
      contractCustomPhone,
      enterpriseAddress,
      enterpriseAreaNum:
        enterpriseAddressList.areaObj.codeObj[enterpriseAreaNum],
      enterpriseCityNum:
        enterpriseAddressList.cityObj.codeObj[enterpriseCityNum],
      enterpriseName,
      enterpriseProvinceNum:
        enterpriseAddressList.provinceObj.codeObj[enterpriseProvinceNum],
      industryId: industryList.valuesObj[industryType],
      intineraryType,
      mailAddress: intineraryType == 1 ? mailAddress : undefined,
      mailName: intineraryType == 1 ? mailName : undefined,
      mailPhone: intineraryType == 1 ? mailPhone : undefined,
      mailAreaNum:
        intineraryType == 1
          ? mailAddressList.areaObj.codeObj[mailAreaNum]
          : undefined,
      mailCityNum:
        intineraryType == 1
          ? mailAddressList.cityObj.codeObj[mailCityNum]
          : undefined,
      mailProvinceNum:
        intineraryType == 1
          ? mailAddressList.provinceObj.codeObj[mailProvinceNum]
          : undefined,
      signatureFid,
      protocolFid,
      createId,
      auditStatus: "1",
      // createId:'chSuperAdmin',
    };

    if (id) {
      const res = await contractCustomerUpdate({ ...params, id });
      if (res.code !== 200) {
        toast.info(res?.message || "操作失败");
        return;
      }
      getResult(res.data);
    } else {
      const res = await contractCustomerAdd(params);
      if (res.code !== 200) {
        toast.info(res?.message || "操作失败");
        return;
      }
      getResult(res.data);
    }
  };

  // 图片上传
  const handleChooseImage = () => {
    Taro.chooseMedia({
      // url:'/api/v1/contractCustomer/upload',
      count: 1, // 最多可以选择的图片张数
      sizeType: ["original", "compressed"], // 图片的尺寸类型
      sourceType: ["album", "camera"], // 选择图片的来源

      success: (res) => {
        // tempFilePaths 为图片的临时文件路径列表

        const tempFilePaths = res?.tempFiles;
        const apiUrl = "/api/v1/contractCustomer/upload";
        const userToken = localStorage.getItem("userInfo") || {};

        Taro.uploadFile({
          url: SERVER_URL + apiUrl.replace(/\/api\//g, `/${APITYPE}/`),
          header: { "access-token": userToken.accessToken },

          filePath: tempFilePaths[0]?.tempFilePath, // 要上传文件资源的路径
          name: "file", // 文件对应的 key，后端通过这个 key 获取文件的二进制内容
          success: (uploadRes) => {
            if (!uploadRes.data) {
              return;
            }
            // 上传成功后的处理
            const { code, data } = JSON?.parse(uploadRes.data);
            if (code === 200) {
              setUploadUrl(data?.path);
              setFormData({
                ...formData,
                businessLicenseFid: data?.id,
              });
            }
          },
          fail: function (error) {
            // 上传失败后的处理
            console.error(error);
          },
        });
      },
    });
  };
  // 签字
  const signConfirm = (url) => {
    const apiUrl = "/api/v1/contractCustomer/upload";
    const userToken = localStorage.getItem("userInfo") || {};

    Taro.uploadFile({
      url: SERVER_URL + apiUrl.replace(/\/api\//g, `/${APITYPE}/`),
      header: { "access-token": userToken.accessToken },

      filePath: url, // 要上传文件资源的路径
      name: "file", // 文件对应的 key，后端通过这个 key 获取文件的二进制内容
      success: (uploadRes) => {
        // 上传成功后的处理
        const { code, data } = JSON.parse(uploadRes.data);
        if (code === 200) {
          setOwnerSignUrl(data?.path);
          setSignImgs(url);
          setSignLabel("2");
          setFormData({
            ...formData,
            signatureFid: data?.id,
          });
        }
      },
      fail: function (error) {
        // 上传失败后的处理
        console.error(error);
      },
    });
    return;
  };

  return (
    <>
      <NavigationBar
        bgColor={"transparent"}
        title={"客户签约"}
        titleColor={"#000"}
        showBackBtn={false}
      />

      <View className="user-center-wrap">
        <View className="head-bg">
          <Image src={signImg} alt="" />
        </View>
        <View className={"sign-content-box"}>
          <View className={"sign-content"}>
            <View className={"sign-title"}>签约公司/单位信息</View>
            <View className={"sign-form"}>
              <Form>
                <View>
                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>企业名称</View>
                    </View>

                    <Input
                      className={"form-input"}
                      placeholderTextColor={"#d1d3e8"}
                      // type="text"
                      placeholder="请输入"
                      value={formData.enterpriseName}
                      onInput={(e) => {
                        setFormData({
                          ...formData,
                          enterpriseName: e.detail.value,
                        });
                      }}
                    />
                  </View>

                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className="form-label">企业地址</View>
                    </View>
                    <View className="address-container">
                      <Picker
                        mode="selector"
                        range={enterpriseAddressList.provinceObj.options}
                        onChange={(e) => {
                          getEnterpriseAddressList("city", e.detail.value);

                          const { options } = enterpriseAddressList.provinceObj;
                          setFormData({
                            ...formData,
                            enterpriseProvinceNum: options[e.detail.value],
                            enterpriseCityNum: undefined,
                            enterpriseAreaNum: undefined,
                          });
                        }}
                      >
                        <View className="picker-address">
                          <View className="form-picker-container">
                            <Input
                              className="form-picker-input"
                              placeholder="省"
                              placeholderClass="x_placeholder"
                              name="province"
                              disabled="true"
                              confirmType="next"
                              value={formData.enterpriseProvinceNum}
                            ></Input>
                            <View className="form-picker-icon-container">
                              {/* <Image
                              className="arrow"
                              src={imgs.rightArrow}
                            ></Image> */}
                              <AtIcon
                                value="chevron-down"
                                size="8"
                                color="#D1D3E8"
                              ></AtIcon>
                            </View>
                          </View>
                        </View>
                      </Picker>
                      <Picker
                        mode="selector"
                        range={enterpriseAddressList.cityObj.options}
                        onChange={(e) => {
                          // getProvinceCityArea("area", e.detail.value);
                          getEnterpriseAddressList("area", e.detail.value);
                          const { cityObj } = enterpriseAddressList;
                          setFormData({
                            ...formData,
                            enterpriseCityNum: cityObj.options[e.detail.value],
                            enterpriseAreaNum: undefined,
                          });
                        }}
                      >
                        <View className="picker-address">
                          <View className="form-picker-container">
                            <Input
                              className="form-picker-input"
                              placeholder="市"
                              placeholderClass="x_placeholder"
                              name="city"
                              disabled="true"
                              confirmType="next"
                              value={formData.enterpriseCityNum}
                            ></Input>
                            <View className="form-picker-icon-container">
                              <AtIcon
                                value="chevron-down"
                                size="8"
                                color="#D1D3E8"
                              ></AtIcon>
                            </View>
                          </View>
                        </View>
                      </Picker>
                      <Picker
                        mode="selector"
                        range={enterpriseAddressList.areaObj.options}
                        onChange={(e) => {
                          const { areaObj } = enterpriseAddressList;
                          setFormData({
                            ...formData,
                            enterpriseAreaNum: areaObj.options[e.detail.value],
                          });
                        }}
                      >
                        <View className="picker-address">
                          <View className="form-picker-container">
                            <Input
                              className="form-picker-input"
                              placeholder="区"
                              placeholderClass="x_placeholder"
                              name="district"
                              disabled="true"
                              confirmType="next"
                              value={formData.enterpriseAreaNum}
                            ></Input>
                            <View className="form-picker-icon-container">
                              <AtIcon
                                value="chevron-down"
                                size="8"
                                color="#D1D3E8"
                              ></AtIcon>
                            </View>
                          </View>
                        </View>
                      </Picker>
                    </View>

                    <View className="form-item">
                      <Input
                        className={"form-input"}
                        placeholderTextColor={"#d1d3e8"}
                        placeholder="请输入详细地址"
                        value={formData.enterpriseAddress}
                        onInput={(e) => {
                          setFormData({
                            ...formData,
                            enterpriseAddress: e.detail.value,
                          });
                        }}
                      />
                    </View>
                  </View>

                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>企业性质</View>
                    </View>
                    <Picker
                      mode="selector"
                      range={industryList.options}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          industryType: industryList.options[e.detail.value],
                        });
                      }}
                    >
                      <View className="form-picker-containers">
                        <Input
                          className="form-picker-input"
                          placeholder="请选择"
                          placeholderClass="x_placeholder"
                          name="industryType"
                          disabled="true"
                          confirmType="next"
                          value={formData.industryType}
                        ></Input>

                        <View className="form-picker-icon-container">
                          <Image
                            className="arrow"
                            src={imgs.rightArrow}
                          ></Image>
                        </View>
                      </View>
                    </Picker>
                  </View>
                  <View className="form-item shipping-address">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>行程单接收方式</View>
                    </View>

                    <View className={"radio-box"}>
                      <RadioSelf
                        dataList={[
                          {
                            label: "统一邮寄",
                            value: "1",
                          },
                          {
                            label: "自行打印",
                            value: "2",
                          },
                        ]}
                        value={formData?.intineraryType}
                        onChange={(val) => {
                          setFormData({
                            ...formData,
                            intineraryType: val,
                          });
                        }}
                      />
                    </View>

                    {formData.intineraryType == 2 ? (
                      <></>
                    ) : (
                      <>
                        <View className="address-container">
                          <Picker
                            mode="selector"
                            range={mailAddressList.provinceObj.options}
                            onChange={(e) => {
                              // getReceiveProvinceCityArea("city", e.detail.value);
                              getMailAddressList("city", e.detail.value);
                              const { provinceObj } = mailAddressList;
                              setFormData({
                                ...formData,
                                mailProvinceNum:
                                  provinceObj.options[e.detail.value],
                                mailCityNum: undefined,
                                mailAreaNum: undefined,
                              });
                            }}
                          >
                            <View className="picker-address">
                              <View className="form-picker-container">
                                <Input
                                  className="form-picker-input"
                                  placeholder="省"
                                  placeholderClass="x_placeholder"
                                  name="province"
                                  disabled="true"
                                  confirmType="next"
                                  value={formData.mailProvinceNum}
                                ></Input>
                                <View className="form-picker-icon-container">
                                  <AtIcon
                                    value="chevron-down"
                                    size="8"
                                    color="#D1D3E8"
                                  ></AtIcon>
                                </View>
                              </View>
                            </View>
                          </Picker>
                          <Picker
                            mode="selector"
                            range={mailAddressList.cityObj.options}
                            onChange={(e) => {
                              // getReceiveProvinceCityArea("area", e.detail.value);
                              getMailAddressList("area", e.detail.value);
                              const { cityObj } = mailAddressList;
                              setFormData({
                                ...formData,
                                mailCityNum: cityObj.options[e.detail.value],
                                mailAreaNum: undefined,
                              });
                            }}
                          >
                            <View className="picker-address">
                              <View className="form-picker-container">
                                <Input
                                  className="form-picker-input"
                                  placeholder="市"
                                  placeholderClass="x_placeholder"
                                  name="city"
                                  disabled="true"
                                  confirmType="next"
                                  value={formData.mailCityNum}
                                ></Input>
                                <View className="form-picker-icon-container">
                                  <AtIcon
                                    value="chevron-down"
                                    size="8"
                                    color="#D1D3E8"
                                  ></AtIcon>
                                </View>
                              </View>
                            </View>
                          </Picker>
                          <Picker
                            mode="selector"
                            range={mailAddressList.areaObj.options}
                            onChange={(e) => {
                              const { areaObj } = mailAddressList;
                              setFormData({
                                ...formData,
                                mailAreaNum: areaObj.options[e.detail.value],
                              });
                            }}
                          >
                            <View className="picker-address">
                              <View className="form-picker-container">
                                <Input
                                  className="form-picker-input"
                                  placeholder="区"
                                  placeholderClass="x_placeholder"
                                  name="district"
                                  disabled="true"
                                  confirmType="next"
                                  value={formData.mailAreaNum}
                                ></Input>
                                <View className="form-picker-icon-container">
                                  <AtIcon
                                    value="chevron-down"
                                    size="8"
                                    color="#D1D3E8"
                                  ></AtIcon>
                                </View>
                              </View>
                            </View>
                          </Picker>
                        </View>

                        <View className="form-item">
                          <Input
                            className={"form-input"}
                            placeholderTextColor={"#d1d3e8"}
                            placeholder="请输入详细地址"
                            value={formData.mailAddress}
                            onInput={(e) => {
                              setFormData({
                                ...formData,
                                mailAddress: e.detail.value,
                              });
                            }}
                          />
                        </View>

                        <View className="form-item">
                          <View className="row">
                            <View className="star">*</View>
                            <View className={"form-label"}>收件人姓名</View>
                          </View>

                          <Input
                            className={"form-input"}
                            placeholderTextColor={"#d1d3e8"}
                            placeholder="请输入"
                            value={formData.mailName}
                            onInput={(e) => {
                              setFormData({
                                ...formData,
                                mailName: e.detail.value,
                              });
                            }}
                          />
                        </View>
                        <View className="form-item">
                          <View className="row">
                            <View className="star">*</View>
                            <View className={"form-label"}>收件人电话</View>
                          </View>

                          <Input
                            className={"form-input"}
                            placeholderTextColor={"#d1d3e8"}
                            placeholder="请输入"
                            type="number"
                            value={formData.mailPhone}
                            onInput={(e) => {
                              setFormData({
                                ...formData,
                                mailPhone: e.detail.value,
                              });
                            }}
                            onBlur={(e) => {
                              const inputValue = e.detail.value;
                              const phoneRegex = /^1[0-9]{10}$/; // 手机号格式验证正则表达式

                              if (phoneRegex.test(inputValue)) {
                                // 如果手机号格式正确，更新表单数据
                                setFormData({
                                  ...formData,
                                  mailPhone: inputValue,
                                });
                              } else {
                                // 如果手机号格式不正确，可以做出相应的处理，比如提示用户
                                toast.info("请输入有效的手机号码");
                              }
                            }}
                          />
                        </View>
                      </>
                    )}
                  </View>

                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>签约人姓名</View>
                    </View>

                    <Input
                      className={"form-input"}
                      placeholderTextColor={"#d1d3e8"}
                      placeholder="请输入"
                      value={formData.contractCustomName}
                      onInput={(e) => {
                        setFormData({
                          ...formData,
                          contractCustomName: e.detail.value,
                        });
                      }}
                    />
                  </View>
                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>签约人电话</View>
                    </View>

                    <Input
                      className={"form-input"}
                      placeholderTextColor={"#d1d3e8"}
                      type="number"
                      placeholder="请输入"
                      value={formData.contractCustomPhone}
                      onInput={(e) => {
                        setFormData({
                          ...formData,
                          contractCustomPhone: e.detail.value,
                        });
                      }}
                      onBlur={(e) => {
                        const inputValue = e.detail.value;
                        const phoneRegex = /^1[0-9]{10}$/; // 手机号格式验证正则表达式

                        if (phoneRegex.test(inputValue)) {
                          // 如果手机号格式正确，更新表单数据
                          setFormData({
                            ...formData,
                            contractCustomPhone: inputValue,
                          });
                        } else {
                          // 如果手机号格式不正确，可以做出相应的处理，比如提示用户
                          toast.info("请输入有效的手机号码");
                        }
                      }}
                    />
                  </View>
                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>联系人姓名</View>
                    </View>

                    <Input
                      className={"form-input"}
                      placeholderTextColor={"#d1d3e8"}
                      placeholder="请输入"
                      value={formData.contactName}
                      onInput={(e) => {
                        setFormData({
                          ...formData,
                          contactName: e.detail.value,
                        });
                      }}
                    />
                  </View>
                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>联系人电话</View>
                    </View>

                    <Input
                      className={"form-input"}
                      placeholderTextColor={"#d1d3e8"}
                      placeholder="请输入"
                      type="number"
                      value={formData.contactPhone}
                      onInput={(e) => {
                        setFormData({
                          ...formData,
                          contactPhone: e.detail.value,
                        });
                      }}
                      onBlur={(e) => {
                        const inputValue = e.detail.value;
                        const phoneRegex = /^1[0-9]{10}$/; // 手机号格式验证正则表达式

                        if (phoneRegex.test(inputValue)) {
                          // 如果手机号格式正确，更新表单数据
                          setFormData({
                            ...formData,
                            contactPhone: inputValue,
                          });
                        } else {
                          // 如果手机号格式不正确，可以做出相应的处理，比如提示用户
                          toast.info("请输入有效的手机号码");
                        }
                      }}
                    />
                  </View>
                  <View className="form-item">
                    <View className="row">
                      <View className="star">*</View>
                      <View className={"form-label"}>公司营业执照</View>
                    </View>

                    {formData.businessLicenseFid ? (
                      <Image
                        src={uploadUrl}
                        mode="aspectFill"
                        className="upload-img"
                        onClick={handleChooseImage}
                      />
                    ) : (
                      <View onClick={handleChooseImage} className="sign-add">
                        <AtIcon value="add" size="16" color="#D1D3E8"></AtIcon>
                      </View>
                    )}
                  </View>
                </View>
              </Form>
            </View>
          </View>

          <View className={"sign-content"}>
            <View className={"sign-title"}>合同协议</View>
            <View className={"sign-form contact-content"}>
              <Contract>
                <View
                  className={"contract-read"}
                  onClick={() => setIsReadContract(!isReadContract)}
                >
                  <View
                    className={`read-checkbox ${
                      isReadContract ? "checked" : ""
                    }`}
                  >
                    {isReadContract && (
                      <Image src={rightIcon} className={"right-icon"} />
                    )}
                  </View>
                  <Text>已阅读并同意相关产品规则</Text>
                </View>
              </Contract>
            </View>
          </View>

          <View className={"sign-content"}>
            <View className={"sign-title"}>客户确认</View>
            <View className={"sign-form"}>
              <View className="form-item">
                <View className="row">
                  <View className="star">*</View>
                  <View className={"form-label"}>
                    使用方法人代表或法人授权代表签字
                  </View>
                </View>
                <View>
                  {signLabel === "1" ? (
                    <View onClick={() => setSignLabel("3")} className="sign">
                      点击签字
                    </View>
                  ) : signLabel === "2" ? (
                    <Image
                      className="sign-img"
                      src={ownerSignUrl}
                      onClick={() => {
                        setSignLabel("3");
                        // setOwnerSignUrl(undefined);
                        // setFormData({
                        //   ...formData,
                        //   signatureFid: undefined,
                        // });
                      }}
                    />
                  ) : (
                    <CanvasSign
                      signConfirm={signConfirm}
                      onBack={() => {
                        if (ownerSignUrl) {
                          setSignLabel("2");
                        } else {
                          setSignLabel("1");
                        }
                      }}
                    />
                  )}
                </View>
              </View>
            </View>
          </View>
          <Button
            type="primary"
            onClick={(e) => {
              if (!isReadContract) {
                toast.info("请先阅读并同意相关产品规则");
                return;
              }
              Taro.showModal({
                title: "提示",
                content: "信息提交后无法再次编辑请确认信息完整后提交",
                confirmText: "确认提交",
                success: (res) => {
                  if (res?.confirm) {
                    handleSubmit(e);
                  }
                },
              });
            }}
            className={"form-btn"}
          >
            提交
          </Button>
        </View>
        <OutPdf
          signImg={signImgs}
          setFormData={setFormData}
          formData={formData}
        />
        <Canvas
          canvasId="picCanvas"
          style="width: 40%; height: 40%"
          ref={picCanvasRef}
        />
      </View>
    </>
  );
};
export default memo(SignForm);
