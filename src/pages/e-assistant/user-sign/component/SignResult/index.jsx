import { memo, useEffect, useState } from "react";
import "./index.less";
import { View, Image } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { toast } from "@/utils/utils";
import {
  successImg,
  waitImg,
  errorImg,
  subscriptionImg,
  rejectMsg,
} from "../../../img";
import NavigationBar from "../../../../common/navigation-bar";
import {
  getContractCustomerInfo, getDecryptWorkNumberKey,
  saveOpenId,
} from "../../../../../services/eAssistant";
import { REQUEST_SUCCESS } from "../../../../common";

const url =
  Taro.getAccountInfoSync().miniProgram.envVersion === "release"
    ? "https://fx.sichuanair.com/mWeb/customer/login"
    : "http://fx.sichuanair.com/mWeb/customer/login";

const SignResult = ({ open, id, reFreshUpload }) => {
  const templateId = "FpaifyIHjwtWZTCcrzNxuErRg6hwyO1pEIIwuLvJn8M";
  const [resultInfo, setResultInfo] = useState({});
  const [subscriptionModal, setSubscriptionModal] = useState(false); //是否订阅
  const [settingModal, setSettingModal] = useState(false); //是否订阅

  const getResultList = async (contractCustomerId) => {
    const { code,data } = await getDecryptWorkNumberKey();
    if (code !== REQUEST_SUCCESS) return
    const res = await getContractCustomerInfo({
      contractCustomerId,
      isDecrypt: true,
      decryptKey:data
    });
    if (res.code !== 200) {
      toast.info(res?.message);
      return;
    }
    // console.log(res.data);
    setResultInfo(res?.data);
  };

  useEffect(() => {
    if (open && id) getResultList(id);
    // getResultList(id)
  }, [open]);

  const renderStatusIcon = (auditStatus) => {
    switch (auditStatus) {
      case "2":
        return <Image src={successImg} className="icon-img" />;
      case "3":
        return <Image src={errorImg} className="icon-img" />;
      case "1":
        return <Image src={waitImg} className="icon-img" />;
      default:
        return <Image src={waitImg} className="icon-img" />;
    }
  };

  const renderStatusBox = (auditStatus) => {
    switch (auditStatus) {
      case "1":
        return <View className="status-box wait-label">等待审核</View>;
      case "2":
        return <View className="status-box success-label">审核成功</View>;
      case "3":
        return <View className="status-box fail-label">审核失败</View>;
      default:
        return <View className="status-box wait-label">等待审核</View>;
    }
  };
  const renderRemark = (remark) => {
    if (remark) {
      return <View className="remark">备注：{remark}</View>;
    }
  };
  const copyToClipboard = (text) => {
    Taro.setClipboardData({
      data: text,
      success: function () {
        Taro.showToast({
          icon: "none",
          title: "链接已复制至粘贴板，前往浏览器粘贴并打开即可查看",
          duration: 2000,
        });
      },
    });
  };

  const renderCopyBox = () => {
    return (
      <View className="copy-box">
        <View className="basic-info">
          <View className="info-item">
            大客户编码：{resultInfo.customerCode}
          </View>
          <View className="info-item">
            联系人姓名：{resultInfo.contactName}
          </View>
          <View className="info-item">
            联系人电话：{resultInfo.contactPhone}
          </View>
          <View>审核成功备注：{resultInfo.reason}</View>
        </View>
        <View className="copy-info">
          <View className="top-label">
            为保障后续机票服务，请复制下方链接并在电脑浏览器中打开后，使用上述信息进行验证并上传您的客户名单。
          </View>

          <View className="bottom-label">
            <View className="ip">{url}</View>
            <View
              className="copy-btn"
              onClick={() => {
                copyToClipboard(url);
              }}
            >
              点击复制
            </View>
          </View>
        </View>
      </View>
    );
  };

  //订阅消息
  const subscribeMessage = () => {
    Taro.getUserInfo({
      success: (res) => {
        //获取openId（需要code来换取）这是用户的唯一标识符
        // 获取code值
        Taro.login({
          //成功放回
          success: async (res) => {
            let code = res.code;
            console.log(code, "====code=====");
            const { code: reqCode, data } = await saveOpenId(code);
            if (reqCode !== REQUEST_SUCCESS || !data) {
              toast.info(res?.message || "获取用户code失败！");
            }
            // // 通过code换取openId
            // Taro.request({
            //   url: `https://api.weixin.qq.com/sns/jscode2session?appid=wxc41efb2b5397a0fb&secret=c87549b97ca286047d782b7b2f832086&js_code=${code}&grant_type=authorization_code`,
            //   success: (res) => {
            //     userInfo.openid = res.data.openid;
            //     console.log(userInfo.openid, "====openid=====");
            //   },
            // });
          },
        });
      },
    });
    Taro.requestSubscribeMessage({
      // tmplIds: [templateId.customer_sign],
      tmplIds: [templateId],
      success: (res) => {
        if (res[templateId] === "reject") {
          setSubscriptionModal(false);
          setTimeout(() => {
            toast.info("未开启消息订阅权限，将无法接收后续消息通知");
          }, 100);
        } else {
          setSettingModal(false);
          setSubscriptionModal(false);
          console.log(res, "----用户授权成功---");
        }
      },
    });
  };
  //用户打开设置页
  const handleUserSetting = () => {
    Taro.openSetting({
      withSubscriptions: true,
      success: (res) => {
        setSettingModal(false);
        subscribeMessage();
      },
      fail: () => {
        setSettingModal(false);
        setTimeout(() => {
          toast.info("未开启消息订阅权限，将无法接收后续消息通知");
        }, 100);
      },
    });
  };

  //判断用户是否订阅
  const judgeUserSubscribe = () => {
    // 获取用户授权状态
    Taro.getSetting({
      withSubscriptions: true,
      success: function (res) {
        console.log(res);
        if (
          res.subscriptionsSetting &&
          res.subscriptionsSetting.mainSwitch &&
          res.subscriptionsSetting.itemSettings
        ) {
          const itemSettings = res.subscriptionsSetting.itemSettings;

          if (itemSettings[templateId] === "reject") {
            console.log("===reject===");
            // 用户拒绝授权
            setSettingModal(true);
          } else if (itemSettings[templateId] === "accept") {
            // 用户已授权
            console.log("===accept===");
            setSubscriptionModal(true);
          } else {
            setSubscriptionModal(true);
          }
        } else {
          setSubscriptionModal(true);
        }
      },
    });
  };
  useEffect(() => {
    judgeUserSubscribe();
  }, []);

  return (
    <>
      <NavigationBar
        bgColor={"#fff"}
        title={"签约结果"}
        titleColor={"#000"}
        showBackBtn={false}
      />
      <View className="result-center-wrap">
        <View className={"result-content"}>
          <View className={"result-title"}>川航集团协议签约状态</View>
          <View className="timeline">
            {/*提交状态*/}
            <View className="timeline-content">
              <View className="timeline-content-top">
                <Image src={successImg} className="icon-img" />
                <View className="timeline-content-top-label">提交状态</View>
              </View>
              <View className="timeline-content-bottom success-color">
                <View className="status-box success-label">提交成功</View>
              </View>
            </View>
            {/*审核状态*/}
            <View className="timeline-content">
              <View className="timeline-content-top">
                {renderStatusIcon(resultInfo?.auditStatus)}
                <View className="timeline-content-top-label">审核状态</View>
              </View>
              <View className={`timeline-content-bottom `}>
                {renderStatusBox(resultInfo?.auditStatus)}
                {renderRemark(resultInfo?.reason)}

                {resultInfo?.auditStatus === "2" ? renderCopyBox() : <></>}
                {resultInfo?.auditStatus === "3" ? (
                  <View
                    className="btn-box"
                    onClick={() => {
                      reFreshUpload(id);
                    }}
                  >
                    重新上传
                  </View>
                ) : (
                  <></>
                )}
              </View>
            </View>
          </View>
        </View>
      </View>
      {subscriptionModal && (
        <View className="subscription-modal">
          <Image
            src={subscriptionImg}
            className={"subscription-img"}
            onClick={() => {
              subscribeMessage();
            }}
          />
        </View>
      )}
      {settingModal && (
        <View className="subscription-modal">
          {/*<Button openType="openSetting" className={"subscription-img"}>*/}
          <Image
            src={rejectMsg}
            className={"subscription-img"}
            onClick={() => handleUserSetting()}
          />
          {/*</Button>*/}
        </View>
      )}
    </>
  );
};
export default memo(SignResult);
